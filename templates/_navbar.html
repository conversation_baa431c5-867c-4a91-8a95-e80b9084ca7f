<!-- 导航栏 - 使用 Tailwind CSS 重构版本 -->
<div class="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 bg-white border-b border-gray-200 shadow-sm transition-all duration-300 flex items-center justify-between w-full max-w-4xl px-8 py-4 min-h-[60px] md:px-4 md:py-3 md:min-h-[50px]" style="background-color: rgba(255, 255, 255, 0.98); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);">
    <!-- 左侧：主导航标签页 -->
    <nav class="flex items-center gap-2 md:gap-1" role="tablist">
        <!-- 主页标签 -->
        <a href="{{ url_for('index') }}"
           class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                  {% if active_page == 'index' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                  {% endif %}
                  md:px-3 md:py-2 md:text-xs"
           role="tab"
           aria-selected="{% if active_page == 'index' %}true{% else %}false{% endif %}">
            <span class="relative z-10">主页</span>
            {% if active_page != 'index' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>

        <!-- 标签页标签 -->
        <a href="{{ url_for('tags') }}"
           class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                  {% if active_page == 'tags' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                  {% endif %}
                  md:px-3 md:py-2 md:text-xs"
           role="tab"
           aria-selected="{% if active_page == 'tags' %}true{% else %}false{% endif %}">
            <span class="relative z-10">标签</span>
            {% if active_page != 'tags' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>

        <!-- 随机页标签 -->
        <a href="{{ url_for('random_post_page') }}"
           class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                  {% if active_page == 'random' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                  {% endif %}
                  md:px-3 md:py-2 md:text-xs"
           role="tab"
           aria-selected="{% if active_page == 'random' %}true{% else %}false{% endif %}">
            <span class="relative z-10">随机</span>
            {% if active_page != 'random' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>

    </nav>

    <!-- 中间：搜索功能 -->
    <div class="flex items-center gap-2 flex-1 max-w-xs mx-4 md:max-w-[200px] md:mx-2">
        <div class="relative flex-1">
            <input type="text"
                   id="navSearchInput"
                   placeholder="搜索微博..."
                   value="{{ request.args.get('q', '') }}"
                   class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-lg transition-all duration-300 bg-white focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-100 md:px-2 md:py-2 md:text-xs md:placeholder:text-xs" />
        </div>
        <button id="navSearchButton"
                class="px-3 py-2.5 text-sm font-medium bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 rounded-lg cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105 active:scale-95 md:px-2 md:py-2 md:text-xs">
            <svg class="w-4 h-4 md:w-3 md:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </button>
    </div>

    <!-- 右侧：退出链接 -->
    <a href="{{ url_for('logout') }}"
       class="group relative text-sm font-medium text-gray-400 no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out hover:text-red-500 hover:bg-red-50 hover:scale-105 md:px-3 md:py-2 md:text-xs">
        <span class="relative z-10">退出</span>
        <div class="absolute inset-0 bg-red-500 rounded-xl opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
    </a>
</div>
