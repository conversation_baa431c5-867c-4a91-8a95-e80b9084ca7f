#!/bin/bash

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

echo_info "开始启动微博应用..."

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo_info "虚拟环境不存在，正在创建..."
    python3 -m venv venv
    check_status "虚拟环境创建成功" "虚拟环境创建失败"
else
    echo_info "虚拟环境已存在"
fi

# 激活虚拟环境
echo_info "激活虚拟环境..."
source venv/bin/activate
check_status "虚拟环境激活成功" "虚拟环境激活失败"

# 检查依赖是否已安装
echo_info "检查项目依赖..."
pip show Flask >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo_info "安装项目依赖..."
    pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
    check_status "依赖安装成功" "依赖安装失败"
else
    echo_info "项目依赖已安装"
fi

# 检查端口占用并清理
echo_info "检查5001端口占用情况..."
PID=$(lsof -t -i:5001)
if [ ! -z "$PID" ]; then
    echo_info "端口5001被占用，正在终止进程(PID: $PID)..."
    kill $PID
    sleep 2
    # 检查进程是否已终止
    if kill -0 $PID 2>/dev/null; then
        echo_info "强制终止进程..."
        kill -9 $PID
        sleep 1
    fi
    echo_success "端口5001已清理"
else
    echo_info "端口5001未被占用"
fi

# 启动Flask应用
echo_info "启动Flask应用..."
echo_info "应用将在 http://localhost:5001 上运行"
echo_info "按 Ctrl+C 停止应用"

export FLASK_APP=app.py
flask run --port 5001